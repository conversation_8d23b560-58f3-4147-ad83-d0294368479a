package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	AirplaneDB2      *mongo.Database
	CustomerDB2      *mongo.Database
	HotelDB2         *mongo.Database
	NotificationDB2  *mongo.Database
	OrderDB2         *mongo.Database
	PartnerDB2       *mongo.Database
	PartnershipDB2   *mongo.Database
	PaymentDB2       *mongo.Database
	SkyhubFlightsDB2 *mongo.Database
	SkyhubHotelsDB2  *mongo.Database
	WalletDB2        *mongo.Database
)

// Connect PROD1
var (
	AirplaneDB1      *mongo.Database
	CustomerDB1      *mongo.Database
	HotelDB1         *mongo.Database
	NotificationDB1  *mongo.Database
	OrderDB1         *mongo.Database
	PartnerDB1       *mongo.Database
	PartnershipDB1   *mongo.Database
	PaymentDB1       *mongo.Database
	SkyhubFlightsDB1 *mongo.Database
	SkyhubHotelsDB1  *mongo.Database
	WalletDB1        *mongo.Database
)

var client2, client1 *mongo.Client
var MONGO_URI_PROD1 = ""
var MONGO_URI_PROD2 = ""
var err error

func ConnectMongoDB(ctx context.Context) {
	client2Opts := options.Client().ApplyURI(MONGO_URI_PROD2)
	client2, err = mongo.Connect(ctx, client2Opts)
	if err != nil {
		log.Fatal("MongoDB connection error:", err)
	}

	// Gán biến tương ứng
	AirplaneDB2 = client2.Database("airplane-db2")
	CustomerDB2 = client2.Database("customer-db2")
	HotelDB2 = client2.Database("hotel-db2")
	NotificationDB2 = client2.Database("notification-db2")
	OrderDB2 = client2.Database("order-db2")
	PartnerDB2 = client2.Database("partner-db2")
	PartnershipDB2 = client2.Database("partnership-db2")
	PaymentDB2 = client2.Database("payment-db2")
	SkyhubFlightsDB2 = client2.Database("skyhub-flights-db2")
	SkyhubHotelsDB2 = client2.Database("skyhub-hotels-db2")
	WalletDB2 = client2.Database("wallet-db2")

	fmt.Println("All PROD2 databases initialized.")

	AirplaneDB2.Client()
	CustomerDB2.Client()
	HotelDB2.Client()
	NotificationDB2.Client()
	OrderDB2.Client()
	PartnerDB2.Client()
	PartnershipDB2.Client()
	PaymentDB2.Client()
	SkyhubFlightsDB2.Client()
	SkyhubHotelsDB2.Client()
	WalletDB2.Client()

	client1Opts := options.Client().ApplyURI(MONGO_URI_PROD1)
	client1, err = mongo.Connect(ctx, client1Opts)
	if err != nil {
		log.Fatal("MongoDB connection error:", err)
	}

	// Gán biến tương ứng
	AirplaneDB1 = client1.Database("airplane-db1")
	CustomerDB1 = client1.Database("customer-db1")
	HotelDB1 = client1.Database("hotel-db1")
	NotificationDB1 = client1.Database("notification-db1")
	OrderDB1 = client1.Database("order-db1")
	PartnerDB1 = client1.Database("partner-db1")
	PartnershipDB1 = client1.Database("partnership-db1")
	PaymentDB1 = client1.Database("payment-db1")
	SkyhubFlightsDB1 = client1.Database("skyhub-flights-db1")
	SkyhubHotelsDB1 = client1.Database("skyhub-hotels-db1")
	WalletDB1 = client1.Database("wallet-db1")

	fmt.Println("All PROD1 databases initialized.")

	// Thực hành
	AirplaneDB1.Client()
	CustomerDB1.Client()
	HotelDB1.Client()
	NotificationDB1.Client()
	OrderDB1.Client()
	PartnerDB1.Client()
	PartnershipDB1.Client()
	PaymentDB1.Client()
	SkyhubFlightsDB1.Client()
	SkyhubHotelsDB1.Client()
	WalletDB1.Client()
}
func RunMongoMergeProdScript() {
	ctx, cancel := context.WithTimeout(context.Background(), 100000*time.Second)
	defer cancel()

	ConnectMongoDB(ctx)
	defer client1.Disconnect(ctx)
	defer client2.Disconnect(ctx)
	hubPartnershipID := "67b6db785acbc94c17c88ab7"
	hubPartnershipObjID, _ := primitive.ObjectIDFromHex(hubPartnershipID)

	biziHubPartnerShopID := "67eb6150bc7f0139eb50957c"
	biziHubPartnerShopObjID, _ := primitive.ObjectIDFromHex(biziHubPartnerShopID)

	biziHubPartnershipID := "67e38115b561ec4d5deef4ff"
	biziHubPartnershipObjID, _ := primitive.ObjectIDFromHex(biziHubPartnershipID)

	biziHubOldPartnershipID := "663b329480f097962b1b50aa"
	biziHubOldPartnershipObjID, _ := primitive.ObjectIDFromHex(biziHubOldPartnershipID)

	listOfficeIDBiziHub, _, _ := MigrateDataPartnerHubConfigFromProd2ToProd1(ctx, biziHubPartnershipObjID)
	_, _, listPartnerShopObjIDs, _, _, _, listOwnerIDs := MigrateDataPartnerShopFromProd2ToProd1(ctx, listOfficeIDBiziHub, biziHubPartnershipID, biziHubPartnershipObjID)
	MigrateDataHotelProd2ToProd1(ctx, listOfficeIDBiziHub, biziHubPartnerShopObjID, hubPartnershipObjID, biziHubPartnershipObjID, listPartnerShopObjIDs)

	MigrateDataHotelHubTransactionProd2ToProd1(ctx, listPartnerShopObjIDs, biziHubPartnershipObjID)
	MigrateDataWalletProd2ToProd1(ctx, listOwnerIDs, biziHubOldPartnershipID, biziHubPartnershipID)
	MigrateDataPaymentProd2ToProd1(ctx, biziHubOldPartnershipID, biziHubPartnershipID)
	MigrateDataPartnershipFromProd2ToProd1(ctx, biziHubOldPartnershipID, biziHubPartnershipID)
	MigrateDataNotificationFromProd2ToProd1(ctx, biziHubOldPartnershipObjID, biziHubPartnershipObjID)
	fmt.Println("Merging data from PROD2 to PROD1 successfully")
}

func MigrateDataPartnerHubConfigFromProd2ToProd1(ctx context.Context, biziHubPartnershipObjID primitive.ObjectID) (listOfficeIDBiziHub []string, listPartnerHubConfig []interface{}, listPartnerHubConfigDuplicate []interface{}) {
	cursor, err := HotelDB2.Collection("partner_hub_configs").Find(ctx, bson.M{})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listOfficeIDBiziHub = append(listOfficeIDBiziHub, doc["office_id"].(string))

		doc["is_migrated"] = true
		doc["partnership_id"] = biziHubPartnershipObjID
		listPartnerHubConfig = append(listPartnerHubConfig, doc)
	}

	cursor, err = AirplaneDB1.Collection("partner_hub_configs").Find(ctx, bson.M{
		"office_id": bson.M{"$in": listOfficeIDBiziHub},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listPartnerHubConfigDuplicate = append(listPartnerHubConfigDuplicate, doc)
	}

	// HOTEL PROD 1
	cursor, err = HotelDB1.Collection("partner_hub_configs").Find(ctx, bson.M{
		"office_id": bson.M{"$in": listOfficeIDBiziHub},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listPartnerHubConfigDuplicate = append(listPartnerHubConfigDuplicate, doc)
	}
	WriteFileDuplicate("partner_hub_config_duplicate.json", listPartnerHubConfigDuplicate)

	listPartnerHubConfig = FilterByFieldNotIn(listPartnerHubConfig, listPartnerHubConfigDuplicate, "office_id")

	err = client1.UseSession(ctx, func(sessionContext mongo.SessionContext) error {
		if err := sessionContext.StartTransaction(); err != nil {
			return err
		}

		if len(listPartnerHubConfig) > 0 {
			_, err := HotelDB1.Collection("partner_hub_configs").InsertMany(sessionContext, listPartnerHubConfig)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}

			_, err = AirplaneDB1.Collection("partner_hub_configs").InsertMany(sessionContext, listPartnerHubConfig)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}

		if err := sessionContext.CommitTransaction(sessionContext); err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		fmt.Println("[MigrateDataPartnerHubConfigFromProd2ToProd1] Transaction failed: %v", err)
	}

	fmt.Println("Merging data PartnerHubConfig from PROD2 to PROD1 successfully")
	return listOfficeIDBiziHub, listPartnerHubConfig, listPartnerHubConfigDuplicate
}

func MigrateDataPartnerShopFromProd2ToProd1(ctx context.Context, listOfficeIDBiziHub []string, biziHubPartnershipID string, biziHubPartnershipObjIDs primitive.ObjectID) (listPartnerShopIDs []string, listPartnerUserIDs []string, listPartnerShopObjIDs []primitive.ObjectID, listPartnerUserObjIDs []primitive.ObjectID, listPartnerShops []interface{}, listPartnerUsers []interface{}, ownerShopIDs []string) {
	cursor, err := PartnerDB2.Collection("partner_shops").Find(ctx, bson.M{
		"office_id": bson.M{"$in": listOfficeIDBiziHub},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listPartnerShopIDs = append(listPartnerShopIDs, doc["_id"].(primitive.ObjectID).Hex())
		listPartnerShopObjIDs = append(listPartnerShopObjIDs, doc["_id"].(primitive.ObjectID))

		if doc["owner"] != nil {
			ownerShopIDs = append(ownerShopIDs, doc["owner"].(string))
		}

		doc["is_migrated"] = true
		doc["partnership_id"] = biziHubPartnershipID
		listPartnerShops = append(listPartnerShops, doc)
	}
	// fmt.Println(listPartnerShopIDs)
	// fmt.Println(listPartnerShopObjIDs)

	var listPartnerShopDuplicate []interface{}
	cursor, err = PartnerDB1.Collection("partner_shops").Find(ctx, bson.M{
		"_id": bson.M{"$in": listPartnerShopObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listPartnerShopDuplicate = append(listPartnerShopDuplicate, doc)
		WriteFileDuplicate("partner_shop_duplicate.json", listPartnerShopDuplicate)
	}

	listPartnerShops = FilterByFieldNotIn(listPartnerShops, listPartnerShopDuplicate, "_id")

	// PARTNER USER
	cursor, err = PartnerDB2.Collection("partner_users").Find(ctx, bson.M{
		"partner_shop_id": bson.M{"$in": listPartnerShopIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listPartnerUserIDs = append(listPartnerUserIDs, doc["_id"].(primitive.ObjectID).Hex())
		listPartnerUserObjIDs = append(listPartnerUserObjIDs, doc["_id"].(primitive.ObjectID))

		doc["is_migrated"] = true
		doc["partnership_id"] = biziHubPartnershipID
		listPartnerUsers = append(listPartnerUsers, doc)
	}

	var listPartnerUserDuplicate []interface{}
	cursor, err = PartnerDB1.Collection("partner_users").Find(ctx, bson.M{
		"_id": bson.M{"$in": listPartnerUserObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		doc["is_migrated"] = true
		doc["partnership_id"] = biziHubPartnershipID
		listPartnerUserDuplicate = append(listPartnerUserDuplicate, doc)

		WriteFileDuplicate("partner_user_duplicate.json", listPartnerUserDuplicate)
	}

	listPartnerUsers = FilterByFieldNotIn(listPartnerUsers, listPartnerUserDuplicate, "_id")

	// PARTNER USER TOKEN
	var listPartnerUserToken []interface{}
	cursor, err = PartnerDB2.Collection("partner_user_tokens").Find(ctx, bson.M{
		"partner_shop_id": bson.M{"$in": listPartnerShopObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listPartnerUserIDs = append(listPartnerUserIDs, doc["_id"].(primitive.ObjectID).Hex())
		listPartnerUserObjIDs = append(listPartnerUserObjIDs, doc["_id"].(primitive.ObjectID))

		doc["is_migrated"] = true
		doc["partnership_id"] = biziHubPartnershipObjIDs
		listPartnerUserToken = append(listPartnerUserToken, doc)
	}

	// PARTNER AGENT GROUP

	var listPartnerAgentGroup []interface{}
	var listPartnerAgentGroupObjIDs []primitive.ObjectID
	cursor, err = PartnerDB2.Collection("partner_agent_groups").Find(ctx, bson.M{
		"partner_shop_id": bson.M{"$in": listPartnerShopObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}

		doc["is_migrated"] = true
		doc["partnership_id"] = biziHubPartnershipObjIDs
		listPartnerAgentGroup = append(listPartnerAgentGroup, doc)
		listPartnerAgentGroupObjIDs = append(listPartnerAgentGroupObjIDs, doc["_id"].(primitive.ObjectID))
	}

	var listPartnerAgentGroupDuplicate []interface{}
	cursor, err = PartnerDB1.Collection("partner_agent_groups").Find(ctx, bson.M{
		"_id": bson.M{"$in": listPartnerAgentGroupObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listPartnerAgentGroupDuplicate = append(listPartnerAgentGroupDuplicate, doc)

		WriteFileDuplicate("partner_agent_group_duplicate.json", listPartnerAgentGroupDuplicate)
	}

	listPartnerAgentGroup = FilterByFieldNotIn(listPartnerAgentGroup, listPartnerAgentGroupDuplicate, "_id")

	// Agent Group wallet
	var listAgentGroupWallet []interface{}
	var listAgentGroupWalletObjIDs []primitive.ObjectID
	cursor, err = PartnerDB2.Collection("agent_group_wallets").Find(ctx, bson.M{
		"partner_shop_id": bson.M{"$in": listPartnerShopObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}

		doc["is_migrated"] = true
		doc["partnership_id"] = biziHubPartnershipObjIDs
		listAgentGroupWallet = append(listAgentGroupWallet, doc)
		listAgentGroupWalletObjIDs = append(listAgentGroupWalletObjIDs, doc["_id"].(primitive.ObjectID))
	}

	var listAgentGroupWalletDuplicate []interface{}
	cursor, err = PartnerDB1.Collection("agent_group_wallets").Find(ctx, bson.M{
		"_id": bson.M{"$in": listAgentGroupWalletObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listAgentGroupWalletDuplicate = append(listAgentGroupWalletDuplicate, doc)

		WriteFileDuplicate("agent_group_wallet_duplicate.json", listAgentGroupWalletDuplicate)
	}

	listAgentGroupWallet = FilterByFieldNotIn(listAgentGroupWallet, listAgentGroupWalletDuplicate, "_id")

	// Agent Group wallet transaction
	var listAgentGroupWalletTransaction []interface{}
	var listAgentGroupWalletTransactionObjIDs []primitive.ObjectID
	cursor, err = PartnerDB2.Collection("agent_group_transactions").Find(ctx, bson.M{
		"partner_shop_id": bson.M{"$in": listPartnerShopObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}

		doc["is_migrated"] = true
		doc["partnership_id"] = biziHubPartnershipObjIDs
		listAgentGroupWalletTransaction = append(listAgentGroupWalletTransaction, doc)
		listAgentGroupWalletTransactionObjIDs = append(listAgentGroupWalletTransactionObjIDs, doc["_id"].(primitive.ObjectID))
	}

	var listAgentGroupWalletTransactionDuplicate []interface{}
	cursor, err = PartnerDB1.Collection("agent_group_transactions").Find(ctx, bson.M{
		"_id": bson.M{"$in": listAgentGroupWalletTransactionObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listAgentGroupWalletTransactionDuplicate = append(listAgentGroupWalletTransactionDuplicate, doc)

		WriteFileDuplicate("agent_group_wallet_transaction_duplicate.json", listAgentGroupWalletTransactionDuplicate)
	}

	listAgentGroupWalletTransaction = FilterByFieldNotIn(listAgentGroupWalletTransaction, listAgentGroupWalletTransactionDuplicate, "_id")
	fmt.Println("Done get partner shop data")

	// Customer

	var listCustomerIDs []string
	var listCustomerObjIDs []primitive.ObjectID
	var listCustomers []interface{}
	cursor, err = CustomerDB2.Collection("customers").Find(ctx, bson.M{
		"_id": bson.M{"$in": listPartnerUserObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listCustomerIDs = append(listCustomerIDs, doc["_id"].(primitive.ObjectID).Hex())
		listCustomerObjIDs = append(listCustomerObjIDs, doc["_id"].(primitive.ObjectID))

		doc["is_migrated"] = true
		doc["partnership_id"] = biziHubPartnershipID
		listCustomers = append(listCustomers, doc)
	}

	var listCustomerDuplicate []interface{}
	cursor, err = CustomerDB1.Collection("customers").Find(ctx, bson.M{
		"_id": bson.M{"$in": listCustomerObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listCustomerDuplicate = append(listCustomerDuplicate, doc)

		WriteFileDuplicate("customer_duplicate.json", listCustomerDuplicate)
	}

	listCustomers = FilterByFieldNotIn(listCustomers, listCustomerDuplicate, "_id")

	fmt.Println("Done get customer user data")

	err = client1.UseSession(ctx, func(sessionContext mongo.SessionContext) error {
		if err := sessionContext.StartTransaction(); err != nil {
			return err
		}

		if len(listPartnerShops) > 0 {
			_, err := PartnerDB1.Collection("partner_shops").InsertMany(sessionContext, listPartnerShops)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}

		if len(listPartnerUsers) > 0 {
			_, err = PartnerDB1.Collection("partner_users").InsertMany(sessionContext, listPartnerUsers)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}

		if len(listPartnerUserToken) > 0 {
			_, err = PartnerDB1.Collection("partner_user_tokens").InsertMany(sessionContext, listPartnerUserToken)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}

		if len(listPartnerAgentGroup) > 0 {
			_, err = PartnerDB1.Collection("partner_agent_groups").InsertMany(sessionContext, listPartnerAgentGroup)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}

		if len(listAgentGroupWallet) > 0 {
			_, err = PartnerDB1.Collection("agent_group_wallets").InsertMany(sessionContext, listAgentGroupWallet)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}

		if len(listAgentGroupWalletTransaction) > 0 {
			_, err = PartnerDB1.Collection("agent_group_wallet_transactions").InsertMany(sessionContext, listAgentGroupWalletTransaction)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}

		if len(listCustomers) > 0 {
			_, err = CustomerDB1.Collection("customers").InsertMany(sessionContext, listCustomers)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}

		if err := sessionContext.CommitTransaction(sessionContext); err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		fmt.Println("[MigrateDataPartnerShopFromProd2ToProd1] Transaction failed: %v", err)
	}

	fmt.Println("Merging data Partner from PROD2 to PROD1 successfully")
	return listPartnerShopIDs, listPartnerUserIDs, listPartnerShopObjIDs, listPartnerUserObjIDs, listPartnerShops, listPartnerUsers, ownerShopIDs
}

func MigrateDataHotelProd2ToProd1(ctx context.Context, listOfficeIDBiziHub []string, biziHubPartnerShopObjID primitive.ObjectID, hubPartnershipObjID, biziPartnershipObjID primitive.ObjectID, listPartnerShopObjIDs []primitive.ObjectID) (listHotelBookings []interface{}, listHubHotelBookings []interface{}, listOrderObjIDs []primitive.ObjectID) {
	var listHotelBookingOrderCodes []string
	var listHubHotelBookingOrderCodes []string
	var listHotelObjIDs []primitive.ObjectID
	cursor, err := HotelDB2.Collection("hotel_bookings").Find(ctx, bson.M{
		"partner_shop_id": bson.M{"$in": listPartnerShopObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}

		listHotelBookingOrderCodes = append(listHotelBookingOrderCodes, doc["order_code"].(string))
		listHotelObjIDs = append(listHotelObjIDs, doc["_id"].(primitive.ObjectID))
		doc["is_migrated"] = true
		doc["partnership_id"] = biziPartnershipObjID
		doc["hub_partner_shop_id"] = biziHubPartnerShopObjID
		doc["hub_partnership_id"] = hubPartnershipObjID
		listHotelBookings = append(listHotelBookings, doc)
	}
	// fmt.Println(listHotelBookings)

	var listHotelBookingDuplicate []interface{}
	cursor, err = HotelDB1.Collection("hotel_bookings").Find(ctx, bson.M{
		"$or": []bson.M{
			{"order_code": bson.M{"$in": listHotelBookingOrderCodes}},
			{"_id": bson.M{"$in": listHotelObjIDs}},
		}})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listHotelBookingDuplicate = append(listHotelBookingDuplicate, doc)
		WriteFileDuplicate("hotel_booking_duplicate.json", listHotelBookingDuplicate)
	}

	listHotelBookings = FilterByFieldNotIn(listHotelBookings, listHotelBookingDuplicate, "order_code")

	// Booking Selection
	var listBookingSelection []interface{}
	var listBookingSelectionObjIDs []primitive.ObjectID
	cursor, err = HotelDB2.Collection("booking_selections").Find(ctx, bson.M{
		"partner_shop_id": bson.M{"$in": listPartnerShopObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}

		listBookingSelectionObjIDs = append(listBookingSelectionObjIDs, doc["_id"].(primitive.ObjectID))
		doc["is_migrated"] = true
		doc["partnership_id"] = biziPartnershipObjID
		listBookingSelection = append(listBookingSelection, doc)
	}

	var listBookingSelectionDuplicate []interface{}
	cursor, err = HotelDB1.Collection("booking_selections").Find(ctx, bson.M{
		"_id": bson.M{"$in": listBookingSelectionObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listBookingSelectionDuplicate = append(listBookingSelectionDuplicate, doc)
		WriteFileDuplicate("booking_selection_duplicate.json", listBookingSelectionDuplicate)
	}

	listBookingSelection = FilterByFieldNotIn(listBookingSelection, listBookingSelectionDuplicate, "_id")

	fmt.Println("Done get hotel booking data")

	// HUB HOTEL
	var listHubHotelObjIDs []primitive.ObjectID
	var listTransactionIDs []string
	cursor, err = SkyhubHotelsDB2.Collection("hub_hotel_orders").Find(ctx, bson.M{
		"office_id": bson.M{"$in": listOfficeIDBiziHub},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}

		listHubHotelBookingOrderCodes = append(listHubHotelBookingOrderCodes, doc["order_code"].(string))
		listHubHotelObjIDs = append(listHubHotelObjIDs, doc["_id"].(primitive.ObjectID))
		orderID, _ := primitive.ObjectIDFromHex(doc["order_payment_id"].(string))
		listOrderObjIDs = append(listOrderObjIDs, orderID)
		listTransactionIDs = append(listTransactionIDs, doc["last_transaction_id"].(string))
		doc["is_migrated"] = true
		listHubHotelBookings = append(listHubHotelBookings, doc)
	}

	var listHubHotelBookingDuplicate []interface{}
	cursor, err = SkyhubHotelsDB1.Collection("hub_hotel_orders").Find(ctx, bson.M{
		"$or": []bson.M{
			{"order_code": bson.M{"$in": listHubHotelBookingOrderCodes}},
			{"_id": bson.M{"$in": listHubHotelObjIDs}},
		}})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listHubHotelBookingDuplicate = append(listHubHotelBookingDuplicate, doc)
		WriteFileDuplicate("hub_hotel_booking_duplicate.json", listHubHotelBookingDuplicate)
	}

	listHubHotelBookings = FilterByFieldNotIn(listHubHotelBookings, listHubHotelBookingDuplicate, "order_code")

	fmt.Println("Done get hub hotel data")

	// ORDER
	var listOrders []interface{}
	cursor, err = OrderDB2.Collection("orders").Find(ctx, bson.M{
		"_id": bson.M{"$in": listOrderObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}

		doc["is_migrated"] = true
		doc["partnership_id"] = hubPartnershipObjID.Hex()
		listOrders = append(listOrders, doc)
	}

	var listOrderDuplicate []interface{}
	cursor, err = OrderDB1.Collection("orders").Find(ctx, bson.M{
		"_id": bson.M{"$in": listOrderObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listOrderDuplicate = append(listOrderDuplicate, doc)
		WriteFileDuplicate("order_duplicate.json", listOrderDuplicate)
	}

	listOrders = FilterByFieldNotIn(listOrders, listOrderDuplicate, "_id")

	fmt.Println("Done get order data")

	var listTransaction []interface{}
	var listTransactionObjIDs []primitive.ObjectID
	cursor, err = WalletDB2.Collection("transactions").Find(ctx, bson.M{
		"transaction_id": bson.M{"$in": listTransactionIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listTransactionObjIDs = append(listTransactionObjIDs, doc["_id"].(primitive.ObjectID))
		doc["is_migrated"] = true
		doc["partnership_id"] = biziPartnershipObjID.Hex()
		listTransaction = append(listTransaction, doc)
	}

	var listTransactionDuplicate []interface{}
	cursor, err = WalletDB1.Collection("transactions").Find(ctx, bson.M{
		"$or": []bson.M{
			{"transaction_id": bson.M{"$in": listTransactionIDs}},
			{"_id": bson.M{"$in": listTransactionObjIDs}},
		},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listTransactionDuplicate = append(listTransactionDuplicate, doc)
		WriteFileDuplicate("transaction_duplicate.json", listTransactionDuplicate)
	}

	listTransaction = FilterByFieldNotIn(listTransaction, listTransactionDuplicate, "transaction_id")

	fmt.Println("Done get transaction data")

	err = client1.UseSession(ctx, func(sessionContext mongo.SessionContext) error {
		if err := sessionContext.StartTransaction(); err != nil {
			return err
		}

		if len(listHotelBookings) > 0 {
			_, err = HotelDB1.Collection("hotel_bookings").InsertMany(sessionContext, listHotelBookings)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}

		if len(listBookingSelection) > 0 {
			_, err = HotelDB1.Collection("booking_selections").InsertMany(sessionContext, listBookingSelection)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}

		if len(listHubHotelBookings) > 0 {
			_, err = SkyhubHotelsDB1.Collection("hub_hotel_orders").InsertMany(sessionContext, listHubHotelBookings)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}

		if len(listOrders) > 0 {
			_, err = OrderDB1.Collection("orders").InsertMany(sessionContext, listOrders)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}

		if len(listTransaction) > 0 {
			_, err = WalletDB1.Collection("transactions").InsertMany(sessionContext, listTransaction)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}

		if err := sessionContext.CommitTransaction(sessionContext); err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		log.Println("[MigrateDataHotelProd2ToProd1] Transaction failed: %v", err)
	}

	fmt.Println("Merging data Hotel from PROD2 to PROD1 successfully")
	return listHotelBookings, listHubHotelBookings, listOrderObjIDs
}

func MigrateDataWalletProd2ToProd1(ctx context.Context, listOwnerIDs []string, biziHubOldPartnershipID, biziHubPartnershipID string) {
	fmt.Println("Merging data Wallet from PROD2 to PROD1")

	var listWallets []interface{}
	var listWalletObjIDs []primitive.ObjectID
	cursor, err := WalletDB2.Collection("wallets").Find(ctx, bson.M{
		"user_id": bson.M{"$in": listOwnerIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listWalletObjIDs = append(listWalletObjIDs, doc["_id"].(primitive.ObjectID))
		doc["is_migrated"] = true
		doc["partnership_id"] = biziHubPartnershipID
		listWallets = append(listWallets, doc)
	}

	var listWalletDuplicate []interface{}
	cursor, err = WalletDB1.Collection("wallets").Find(ctx, bson.M{
		"_id": bson.M{"$in": listWalletObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listWalletDuplicate = append(listWalletDuplicate, doc)
		WriteFileDuplicate("wallet_duplicate.json", listWalletDuplicate)
	}

	listWallets = FilterByFieldNotIn(listWallets, listWalletDuplicate, "_id")

	fmt.Println("Done get wallet data")

	var listPaymentMethods []interface{}
	cursor, err = PaymentDB2.Collection("payment_methods").Find(ctx, bson.M{
		"partnership_id": biziHubOldPartnershipID,
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		doc["is_migrated"] = true
		doc["partnership_id"] = biziHubPartnershipID
		doc["_id"] = primitive.NewObjectID()
		listPaymentMethods = append(listPaymentMethods, doc)
	}

	err = client1.UseSession(ctx, func(sessionContext mongo.SessionContext) error {
		if err := sessionContext.StartTransaction(); err != nil {
			return err
		}
		if len(listWallets) > 0 {
			_, err = WalletDB1.Collection("wallets").InsertMany(sessionContext, listWallets)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}

		if len(listPaymentMethods) > 0 {
			_, err = PaymentDB1.Collection("payment_methods").InsertMany(sessionContext, listPaymentMethods)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}
		if err := sessionContext.CommitTransaction(sessionContext); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		log.Println("[MigrateDataWalletProd2ToProd1] Transaction failed: %v", err)
	}

	fmt.Println("Merging data Wallet from PROD2 to PROD1 successfully")
}

func MigrateDataPaymentProd2ToProd1(ctx context.Context, biziHubOldPartnershipID string, biziHubNewPartnershipID string) {
	fmt.Println("Merging data Payment from PROD2 to PROD1")
	var listNinePayTransactions []interface{}
	var listNinePayTransactionIDs []primitive.ObjectID
	cursor, err := PaymentDB2.Collection("ninepay_transactions").Find(ctx, bson.M{
		"partnership_id": biziHubOldPartnershipID,
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listNinePayTransactionIDs = append(listNinePayTransactionIDs, doc["_id"].(primitive.ObjectID))
		doc["is_migrated"] = true
		doc["partnership_id"] = biziHubNewPartnershipID
		listNinePayTransactions = append(listNinePayTransactions, doc)
	}

	var listNinePayTransactionDuplicate []interface{}
	cursor, err = PaymentDB1.Collection("ninepay_transactions").Find(ctx, bson.M{
		"_id": bson.M{"$in": listNinePayTransactionIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listNinePayTransactionDuplicate = append(listNinePayTransactionDuplicate, doc)
		WriteFileDuplicate("ninepay_transaction_duplicate.json", listNinePayTransactionDuplicate)
	}

	listNinePayTransactions = FilterByFieldNotIn(listNinePayTransactions, listNinePayTransactionDuplicate, "_id")

	fmt.Println("Done get payment data")

	err = client1.UseSession(ctx, func(sessionContext mongo.SessionContext) error {
		if err := sessionContext.StartTransaction(); err != nil {
			return err
		}
		if len(listNinePayTransactions) > 0 {
			_, err = PaymentDB1.Collection("ninepay_transactions").InsertMany(sessionContext, listNinePayTransactions)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}
		if err := sessionContext.CommitTransaction(sessionContext); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		log.Println("[MigrateDataPaymentProd2ToProd1] Transaction failed: %v", err)
	}

	fmt.Println("Merging data Payment from PROD2 to PROD1 successfully")
}

func MigrateDataHotelHubTransactionProd2ToProd1(ctx context.Context, listPartnerShopObjIDs []primitive.ObjectID, biziPartnershipObjID primitive.ObjectID) {
	fmt.Println("Merging data HotelHubTransaction from PROD2 to PROD1")
	// Hub Transaction
	var listHubTransaction []interface{}
	var listHubTransactionObjIDs []primitive.ObjectID
	var listTransactionIDs []string
	cursor, err := HotelDB2.Collection("hub_transactions").Find(ctx, bson.M{
		"partner_shop_id": bson.M{"$in": listPartnerShopObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}

		listHubTransactionObjIDs = append(listHubTransactionObjIDs, doc["_id"].(primitive.ObjectID))
		listTransactionIDs = append(listTransactionIDs, doc["transaction_id"].(string))
		doc["is_migrated"] = true
		doc["partnership_id"] = biziPartnershipObjID.Hex()
		listHubTransaction = append(listHubTransaction, doc)
	}

	var listHubTransactionDuplicate []interface{}
	cursor, err = HotelDB1.Collection("hub_transactions").Find(ctx, bson.M{
		"_id": bson.M{"$in": listHubTransactionObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listHubTransactionDuplicate = append(listHubTransactionDuplicate, doc)
		WriteFileDuplicate("hub_transaction_duplicate.json", listHubTransactionDuplicate)
	}

	listHubTransaction = FilterByFieldNotIn(listHubTransaction, listHubTransactionDuplicate, "_id")

	fmt.Println("Done get hub transaction data")

	// Wallet Transaction
	var listTransactions []interface{}
	cursor, err = WalletDB2.Collection("transactions").Find(ctx, bson.M{
		"transaction_id": bson.M{"$in": listTransactionIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		doc["is_migrated"] = true
		doc["partnership_id"] = biziPartnershipObjID.Hex()
		listTransactions = append(listTransactions, doc)
	}

	var listTransactionsDuplicate []interface{}
	cursor, err = WalletDB1.Collection("transactions").Find(ctx, bson.M{
		"transaction_id": bson.M{"$in": listTransactionIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listTransactionsDuplicate = append(listTransactionsDuplicate, doc)
		WriteFileDuplicate("wallet_transaction_duplicate.json", listTransactionsDuplicate)
	}

	listTransactions = FilterByFieldNotIn(listTransactions, listTransactionsDuplicate, "transaction_id")

	fmt.Println("Done get wallet transaction data")

	err = client1.UseSession(ctx, func(sessionContext mongo.SessionContext) error {
		if err := sessionContext.StartTransaction(); err != nil {
			return err
		}
		if len(listHubTransaction) > 0 {
			_, err = HotelDB1.Collection("hub_transactions").InsertMany(sessionContext, listHubTransaction)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}

		if len(listTransactions) > 0 {
			_, err = WalletDB1.Collection("transactions").InsertMany(sessionContext, listTransactions)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}

		if err := sessionContext.CommitTransaction(sessionContext); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		log.Println("[MigrateDataHotelHubTransactionProd2ToProd1] Transaction failed: %v", err)
	}
	fmt.Println("Merging data HotelHubTransaction from PROD2 to PROD1 successfully")
}
func MigrateDataPartnershipFromProd2ToProd1(ctx context.Context, biziHubOldPartnershipID, biziHubPartnershipID string) {
	fmt.Println("Merging data Partnership from PROD2 to PROD1")

	var listPartnershipUsers []interface{}
	var listPartnershipUserObjIDs []primitive.ObjectID
	cursor, err := PartnershipDB2.Collection("partnership_users").Find(ctx, bson.M{
		"partnership_id": biziHubOldPartnershipID,
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listPartnershipUserObjIDs = append(listPartnershipUserObjIDs, doc["_id"].(primitive.ObjectID))
		doc["is_migrated"] = true
		doc["partnership_id"] = biziHubPartnershipID
		listPartnershipUsers = append(listPartnershipUsers, doc)
	}

	var listPartnershipUserDuplicate []interface{}
	cursor, err = PartnershipDB1.Collection("partnership_users").Find(ctx, bson.M{
		"_id": bson.M{"$in": listPartnershipUserObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listPartnershipUserDuplicate = append(listPartnershipUserDuplicate, doc)
		WriteFileDuplicate("partnership_user_duplicate.json", listPartnershipUserDuplicate)
	}

	listPartnershipUsers = FilterByFieldNotIn(listPartnershipUsers, listPartnershipUserDuplicate, "_id")

	fmt.Println("Done get partnership data")

	err = client1.UseSession(ctx, func(sessionContext mongo.SessionContext) error {
		if err := sessionContext.StartTransaction(); err != nil {
			return err
		}
		if len(listPartnershipUsers) > 0 {
			_, err = PartnershipDB1.Collection("partnership_users").InsertMany(sessionContext, listPartnershipUsers)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}
		if err := sessionContext.CommitTransaction(sessionContext); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		log.Println("[MigrateDataPartnershipFromProd2ToProd1] Transaction failed: %v", err)
	}

	fmt.Println("Merging data Partnership from PROD2 to PROD1 successfully")
}

func MigrateDataNotificationFromProd2ToProd1(ctx context.Context, biziHubOldPartnershipID, biziHubPartnershipID primitive.ObjectID) {
	fmt.Println("Merging data Notification from PROD2 to PROD1")
	var listSmtpConfigs []interface{}
	var listSmtpConfigObjIDs []primitive.ObjectID
	cursor, err := NotificationDB2.Collection("smtp_configs").Find(ctx, bson.M{
		"partnership_id": biziHubOldPartnershipID,
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listSmtpConfigObjIDs = append(listSmtpConfigObjIDs, doc["_id"].(primitive.ObjectID))
		doc["is_migrated"] = true
		doc["partnership_id"] = biziHubPartnershipID
		listSmtpConfigs = append(listSmtpConfigs, doc)
	}

	var listSmtpConfigDuplicate []interface{}
	cursor, err = NotificationDB1.Collection("smtp_configs").Find(ctx, bson.M{
		"_id": bson.M{"$in": listSmtpConfigObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listSmtpConfigDuplicate = append(listSmtpConfigDuplicate, doc)
		WriteFileDuplicate("smtp_config_duplicate.json", listSmtpConfigDuplicate)
	}

	listSmtpConfigs = FilterByFieldNotIn(listSmtpConfigs, listSmtpConfigDuplicate, "_id")

	fmt.Println("Done get smtp config data")

	var listMailTemplates []interface{}
	var listMailTemplateObjIDs []primitive.ObjectID
	cursor, err = NotificationDB2.Collection("email_templates").Find(ctx, bson.M{
		"partnership_id": biziHubOldPartnershipID,
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listMailTemplateObjIDs = append(listMailTemplateObjIDs, doc["_id"].(primitive.ObjectID))
		doc["is_migrated"] = true
		doc["partnership_id"] = biziHubPartnershipID
		listMailTemplates = append(listMailTemplates, doc)
	}

	var listMailTemplateDuplicate []interface{}
	cursor, err = NotificationDB1.Collection("email_templates").Find(ctx, bson.M{
		"_id": bson.M{"$in": listMailTemplateObjIDs},
	})
	if err != nil {
		log.Fatal(err)
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			log.Println("Decode error:", err)
			continue
		}
		listMailTemplateDuplicate = append(listMailTemplateDuplicate, doc)
		WriteFileDuplicate("mail_template_duplicate.json", listMailTemplateDuplicate)
	}

	listMailTemplates = FilterByFieldNotIn(listMailTemplates, listMailTemplateDuplicate, "_id")

	fmt.Println("Done get mail template data")

	err = client1.UseSession(ctx, func(sessionContext mongo.SessionContext) error {
		if err := sessionContext.StartTransaction(); err != nil {
			return err
		}
		if len(listSmtpConfigs) > 0 {
			_, err = NotificationDB1.Collection("smtp_configs").InsertMany(sessionContext, listSmtpConfigs)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}
		if len(listMailTemplates) > 0 {
			_, err = NotificationDB1.Collection("email_templates").InsertMany(sessionContext, listMailTemplates)
			if err != nil {
				_ = sessionContext.AbortTransaction(sessionContext)
				return err
			}
		}
		return sessionContext.CommitTransaction(sessionContext)
	})
	if err != nil {
		log.Println("[MigrateDataNotificationFromProd2ToProd1] Transaction failed: %v", err)
	}

	fmt.Println("Done migrate notification data")

}
func FilterByFieldNotIn(all []interface{}, duplicates []interface{}, field string) []interface{} {
	fieldSet := make(map[interface{}]struct{})

	// Build set từ duplicates
	for _, item := range duplicates {
		if doc, ok := item.(bson.M); ok {
			if val, ok := doc[field]; ok {
				fieldSet[val] = struct{}{}
			}
		}
	}

	// Lọc all, giữ lại item không nằm trong set
	var filtered []interface{}
	for _, item := range all {
		if doc, ok := item.(bson.M); ok {
			val, exists := doc[field]
			if exists {
				if _, isDuplicate := fieldSet[val]; isDuplicate {
					continue
				}
			}
		}
		filtered = append(filtered, item)
	}

	return filtered
}

func WriteFileDuplicate(fileName string, data []interface{}) {
	if len(data) > 0 {
		// Ghi thông tin listPartnerAgentGroupDuplicate ra file JSON
		file, err := os.Create(fileName)
		if err != nil {
			log.Fatal(err)
		}
		defer file.Close()

		jsonData, err := json.Marshal(data)
		if err != nil {
			log.Fatal(err)
		}

		_, err = file.Write(jsonData)
		if err != nil {
			log.Fatal(err)
		}
	}
}
