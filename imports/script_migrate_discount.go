package imports

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type InputHotel struct {
	Status     int    `json:"status"`
	ExternalID string `json:"externalId"`
	Name       struct {
		Code  string `json:"code"`
		Value struct {
			En string `json:"en"`
			Vi string `json:"vi"`
		} `json:"value"`
		SearchText     string `json:"searchText"`
		SkipSetContent bool   `json:"skipSetContent"`
	} `json:"name"`
}

func ImportHotelsFromJSON(filePath string, mongoURI string, dbName string, collectionName string) error {
	// Load file JSON
	data, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read JSON file: %w", err)
	}

	var hotels []InputHotel
	if err := json.Unmarshal(data, &hotels); err != nil {
		return fmt.Errorf("failed to parse JSON: %w", err)
	}

	// Kết nối MongoDB
	client, err := mongo.Connect(context.TODO(), options.Client().ApplyURI(mongoURI))
	if err != nil {
		return fmt.Errorf("failed to connect to MongoDB: %w", err)
	}
	defer client.Disconnect(context.TODO())

	collection := client.Database(dbName).Collection(collectionName)

	// Thời gian hiện tại
	now := time.Now().UnixMilli()

	// Tạo documents mới
	var docs []interface{}
	partnershipObjID, _ := primitive.ObjectIDFromHex("67b6db785acbc94c17c88ab7")
	for _, h := range hotels {
		doc := bson.M{
			"partnership_id": partnershipObjID,
			"created_at":     now,
			"updated_at":     now,
			"office_id":      "",
			"type":           2,
			"config": bson.M{
				"location_type": 1,
				"country_code":  []string{"VN"},
				"rating":        nil,
				"hotel_type":    nil,
			},
			"provider":         11,
			"hotel_id":         h.ExternalID,
			"hotel_name":       h.Name.Value.Vi,
			"amount":           0,
			"percent":          0.08,
			"is_import_manual": true,
		}
		docs = append(docs, doc)
	}

	// Insert vào MongoDB
	if len(docs) > 0 {
		_, err = collection.InsertMany(context.TODO(), docs)
		if err != nil {
			return fmt.Errorf("failed to insert documents: %w", err)
		}
		fmt.Printf("✅ Imported %d documents successfully.\n", len(docs))
	} else {
		fmt.Println("⚠️ No documents to import.")
	}

	return nil
}
