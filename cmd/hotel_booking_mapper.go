package main

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"
)

// BookingStatus mapping
var bookingStatusMap = map[string]string{
	"Success":    "1",
	"Pending":    "2",
	"Failed":     "3",
	"Cancelled":  "4",
	"Draft":      "5",
	"Confirmed":  "6",
	"Refunded":   "7",
	"Expired":    "8",
	"InProgress": "9",
	"Canceling":  "10",
}

// Provider mapping
var providerMap = map[string]string{
	"11": "Expedia",
	"14": "Ratehawk",
}

// HotelBooking represents a record from hotel-db1.hotel_bookings2.csv
type HotelBooking struct {
	ID                              string
	OrderCode                       string
	HubOrderCode                    string
	TotalNights                     string
	InvoicingInformation            string
	CreatedAt                       string
	TransactionCreatedAt            string
	CheckInFull                     string
	CheckOutFull                    string
	RoomConfirmationIds             string
	HotelName                       string
	HotelCity                       string
	HotelCountryCode                string
	RoomName                        string
	RoomQuantity                    string
	OccupancyInfo                   string
	OwnerInfoPhone                  string
	RoomsHolderFullName             string
	RoomsHolderPhone                string
	BookingStatus                   string
	AgentCode                       string
	OwnerInfoUsername               string
	OwnerInfoName                   string
	OwnerInfoEmail                  string
	CAInfoId                        string
	CAInfoName                      string
	InvoiceAgentInfoCompanyName     string
	InvoiceAgentInfoCompanyTaxCode  string
	InvoiceAgentInfoCompanyAddress  string
	InvoiceAgentInfoReceiverName    string
	InvoiceAgentInfoReceiverPhone   string
	InvoiceAgentInfoReceiverEmail   string
	InvoiceAgentInfoReceiverAddress string
	InvoiceAgentInfoReceiverNote    string
}

// HubHotelOrder represents a record from skyhub-hotels-db1.hub_hotel_orders.csv
type HubHotelOrder struct {
	ID                                string
	OrderCode                         string
	ReservationCode                   string
	Provider                          string
	RateTaxesTotal                    string
	RateTaxCurrency                   string
	DiscountOnBaseRateTotal           string
	TotalRateAmount                   string
	TotalRateBasic                    string
	TotalTaxAmount                    string
	PayNow                            string
	TotalPayAtHotel                   string
	PayAtHotelCurrency                string
	HiddenFeeAmount                   string
	AppliedHiddenFeeOriginalPrice     string
	AppliedHiddenFeeFinalPrice        string
	AppliedHiddenFeeAmount            string
	AppliedHiddenFeePercent           string
	AppliedHiddenFeeProvider          string
	DiscountAmount                    string
	AppliedHiddenFeeLocation          string
	AppliedHiddenFeeAccommodationType string
	AppliedHiddenFeeRating            string
	AppliedHiddenFeeCountryCode       string
	AppliedHiddenFeeHotelName         string
	AppliedHiddenFeeUpdatedAt         string
	AppliedHiddenFeeHotelId           string
	AppliedDiscountOriginalPrice      string
	AppliedDiscountFinalPrice         string
	AppliedDiscountId                 string
	AppliedDiscountType               string
	AppliedDiscountLocation           string
	AppliedDiscountAccommodationType  string
	AppliedDiscountRating             string
	AppliedDiscountCountryCode        string
	AppliedDiscountHotelName          string
	AppliedDiscountAmount             string
	AppliedDiscountPercent            string
	AppliedDiscountProvider           string
	AppliedDiscountOfficeId           string
	AppliedDiscountCreatedAt          string
	AppliedDiscountUpdatedAt          string
	AppliedDiscountHotelId            string
	Refunded                          string
}

// MappedRecord represents the output record with mapped fields
type MappedRecord struct {
	OrderCreationTime                string
	PaymentSuccessTime               string
	ServiceOrderCode                 string
	HUBOrderCode                     string
	HotelReservationConfirmationCode string
	ExpediaReservationCode           string
	AgentCustomerCode                string
	AgentCustomerName                string
	BranchCode                       string
	BranchName                       string
	CheckInTime                      string
	CheckOutTime                     string
	HotelName                        string
	Destination                      string
	HotelCountryCode                 string
	RoomType                         string
	RoomQuantity                     string
	GuestCount                       string
	NumberOfNights                   string
	Username                         string
	BookerFullName                   string
	BookerEmail                      string
	BookerPhone                      string
	GuestName                        string
	GuestEmail                       string
	GuestPhone                       string
	BookingStatus                    string
	Provider                         string
	AgentInvoiceInformation          string
	CustomerInvoiceInformation       string
	// New fields from hub_hotel_orders
	TotalNightlyRateAmount              string
	NightlyRateBasic                    string
	NightlyTaxAmount                    string
	CurrencyAfterExchange               string
	HotelTaxesFees                      string
	PayAtHotel                          string
	Surcharges                          string
	PriceBeforeDiscount                 string
	ProviderMarketingFee                string
	TotalRateAmountAllRoomsNights       string
	TotalRateBasicAllRoomsNights        string
	TotalTaxAmountAllRoomsNights        string
	SalePriceForHNHBiziHUB              string
	TotalPayAtHotel                     string
	PayAtHotelCurrency                  string
	TotalHiddenFeeHUBConfig             string
	TotalDiscountFeeHUBConfig           string
	TotalWithTaxBeforeHiddenFee         string
	TotalWithTaxAfterHiddenFee          string
	HiddenFeeID                         string
	HiddenFeeType                       string
	HiddenFeeLocation                   string
	HiddenFeeAccommodationType          string
	HiddenFeeRating                     string
	HiddenFeeCountryCode                string
	HiddenFeeHotelName                  string
	HiddenFeeAmount                     string
	HiddenFeePercent                    string
	HiddenFeeProvider                   string
	HiddenFeeOfficeID                   string
	HiddenFeeCreatedAt                  string
	HiddenFeeUpdatedAt                  string
	HiddenFeeHotelID                    string
	DiscountID                          string
	TotalWithTaxHiddenFeeBeforeDiscount string
	TotalWithTaxHiddenFeeAfterDiscount  string
	DiscountType                        string
	DiscountLocation                    string
	DiscountAccommodationType           string
	DiscountRating                      string
	DiscountCountryCode                 string
	DiscountHotelName                   string
	DiscountAmount                      string
	DiscountPercent                     string
	DiscountProvider                    string
	DiscountOfficeID                    string
	DiscountCreatedAt                   string
	DiscountUpdatedAt                   string
	DiscountHotelID                     string
	IsRefunded                          string
	ExchangeRate                        string
	Profit                              string
}

func main() {
	log.Println("Bắt đầu mapping hotel booking reports...")

	// Đọc file hub hotel orders để tạo lookup map
	hubOrdersMap, err := loadHubHotelOrders("files/skyhub-hotels-db1.hub_hotel_orders.csv")
	if err != nil {
		log.Fatalf("Lỗi khi đọc hub hotel orders: %v", err)
	}
	log.Printf("Đã load %d hub hotel orders", len(hubOrdersMap))

	// Đọc file hotel bookings
	bookings, err := loadHotelBookings("files/hotel-db1.hotel_bookings2.csv")
	if err != nil {
		log.Fatalf("Lỗi khi đọc hotel bookings: %v", err)
	}
	log.Printf("Đã load %d hotel bookings", len(bookings))

	// Xử lý mapping với goroutines
	mappedRecords := processBookingsWithGoroutines(bookings, hubOrdersMap)
	log.Printf("Đã mapping %d records", len(mappedRecords))

	// Ghi output ra file CSV
	outputFile := fmt.Sprintf("files/mapped_hotel_bookings_%s.csv", time.Now().Format("20060102_150405"))
	err = writeMappedRecords(mappedRecords, outputFile)
	if err != nil {
		log.Fatalf("Lỗi khi ghi file output: %v", err)
	}

	log.Printf("Hoàn thành! File output: %s", outputFile)
}

func loadHubHotelOrders(filename string) (map[string]*HubHotelOrder, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return nil, err
	}

	hubOrdersMap := make(map[string]*HubHotelOrder)

	// Skip header row
	for i := 1; i < len(records); i++ {
		record := records[i]
		if len(record) < 44 { // Ensure we have enough columns for all fields
			continue
		}

		hubOrder := &HubHotelOrder{
			ID:                                record[0],
			OrderCode:                         record[1],
			ReservationCode:                   record[2],
			RateTaxesTotal:                    record[3],
			RateTaxCurrency:                   record[4],
			DiscountOnBaseRateTotal:           record[5],
			TotalRateAmount:                   record[6],
			TotalRateBasic:                    record[7],
			TotalTaxAmount:                    record[8],
			PayNow:                            record[9],
			TotalPayAtHotel:                   record[10],
			PayAtHotelCurrency:                record[11],
			HiddenFeeAmount:                   record[12],
			AppliedHiddenFeeOriginalPrice:     record[13],
			AppliedHiddenFeeFinalPrice:        record[14],
			AppliedHiddenFeeAmount:            record[15],
			AppliedHiddenFeePercent:           record[16],
			AppliedHiddenFeeProvider:          record[17],
			Provider:                          record[19], // provider column
			DiscountAmount:                    record[21],
			AppliedHiddenFeeLocation:          record[22],
			AppliedHiddenFeeAccommodationType: record[23],
			AppliedHiddenFeeRating:            record[24],
			AppliedHiddenFeeCountryCode:       record[25],
			AppliedHiddenFeeHotelName:         record[26],
			AppliedHiddenFeeUpdatedAt:         record[27],
			AppliedHiddenFeeHotelId:           record[28],
			AppliedDiscountOriginalPrice:      record[29],
			AppliedDiscountFinalPrice:         record[30],
			AppliedDiscountId:                 record[32],
			AppliedDiscountType:               record[33],
			AppliedDiscountLocation:           record[34],
			AppliedDiscountAccommodationType:  record[35],
			AppliedDiscountRating:             record[36],
			AppliedDiscountCountryCode:        record[37],
			AppliedDiscountHotelName:          record[38],
			AppliedDiscountAmount:             record[39],
			AppliedDiscountPercent:            record[40],
			AppliedDiscountProvider:           record[41],
			AppliedDiscountOfficeId:           record[42],
			AppliedDiscountCreatedAt:          record[43],
			AppliedDiscountUpdatedAt:          record[44],
		}

		// Set refunded status based on cancelled_at field (column 18)
		if len(record) > 18 && record[18] != "" {
			hubOrder.Refunded = "1" // Has been cancelled/refunded
		} else {
			hubOrder.Refunded = "0" // Not refunded
		}

		// Map by order_code for lookup
		if hubOrder.OrderCode != "" {
			hubOrdersMap[hubOrder.OrderCode] = hubOrder
		}
	}

	return hubOrdersMap, nil
}

func loadHotelBookings(filename string) ([]*HotelBooking, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return nil, err
	}

	var bookings []*HotelBooking

	// Skip header row
	for i := 1; i < len(records); i++ {
		record := records[i]
		if len(record) < 60 { // Ensure we have enough columns
			continue
		}

		booking := &HotelBooking{
			ID:                   record[0],
			OrderCode:            record[1],
			HubOrderCode:         record[2],
			TotalNights:          record[3],
			InvoicingInformation: record[4],
			CreatedAt:            record[5],
			TransactionCreatedAt: record[6],
			CheckInFull:          record[7],
			CheckOutFull:         record[8],
			RoomConfirmationIds:  record[9],
			HotelName:            record[10],
			HotelCity:            record[11],
			HotelCountryCode:     record[12],
			RoomName:             record[13],
			RoomQuantity:         record[14],
			OccupancyInfo:        record[15],
			OwnerInfoPhone:       record[16],
			RoomsHolderFullName:  record[17],
			RoomsHolderPhone:     record[18],
			BookingStatus:        record[19],
			AgentCode:            record[45],
			OwnerInfoUsername:    record[46],
			OwnerInfoName:        record[47],
			OwnerInfoEmail:       record[48],
			CAInfoId:             record[58],
			CAInfoName:           record[59],
		}

		// Extract invoice agent info if available (columns 89-96)
		if len(record) > 96 {
			booking.InvoiceAgentInfoCompanyName = record[89]
			booking.InvoiceAgentInfoCompanyTaxCode = record[90]
			booking.InvoiceAgentInfoCompanyAddress = record[91]
			booking.InvoiceAgentInfoReceiverName = record[92]
			booking.InvoiceAgentInfoReceiverPhone = record[93]
			booking.InvoiceAgentInfoReceiverEmail = record[94]
			booking.InvoiceAgentInfoReceiverAddress = record[95]
			booking.InvoiceAgentInfoReceiverNote = record[96]
		}

		bookings = append(bookings, booking)
	}

	return bookings, nil
}

func processBookingsWithGoroutines(bookings []*HotelBooking, hubOrdersMap map[string]*HubHotelOrder) []*MappedRecord {
	const numWorkers = 10 // Số goroutines để xử lý song song

	bookingChan := make(chan *HotelBooking, len(bookings))
	resultChan := make(chan *MappedRecord, len(bookings))

	var wg sync.WaitGroup

	// Start workers
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for booking := range bookingChan {
				mapped := mapBookingRecord(booking, hubOrdersMap)
				resultChan <- mapped
			}
		}()
	}

	// Send bookings to workers
	go func() {
		for _, booking := range bookings {
			bookingChan <- booking
		}
		close(bookingChan)
	}()

	// Wait for all workers to finish
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// Collect results
	var mappedRecords []*MappedRecord
	for mapped := range resultChan {
		mappedRecords = append(mappedRecords, mapped)
	}

	return mappedRecords
}

func mapBookingRecord(booking *HotelBooking, hubOrdersMap map[string]*HubHotelOrder) *MappedRecord {
	mapped := &MappedRecord{
		OrderCreationTime:                booking.CreatedAt,
		PaymentSuccessTime:               booking.TransactionCreatedAt,
		ServiceOrderCode:                 booking.OrderCode,
		HUBOrderCode:                     booking.HubOrderCode,
		HotelReservationConfirmationCode: booking.RoomConfirmationIds,
		AgentCustomerCode:                booking.AgentCode,
		AgentCustomerName:                "", // Cần lookup từ partner-db.partner_shops
		BranchCode:                       booking.CAInfoId,
		BranchName:                       booking.CAInfoName,
		CheckInTime:                      booking.CheckInFull,
		CheckOutTime:                     booking.CheckOutFull,
		HotelName:                        booking.HotelName,
		Destination:                      booking.HotelCity,
		HotelCountryCode:                 booking.HotelCountryCode,
		RoomType:                         booking.RoomName,
		RoomQuantity:                     booking.RoomQuantity,
		GuestCount:                       extractGuestCount(booking.OccupancyInfo),
		NumberOfNights:                   booking.TotalNights,
		Username:                         booking.OwnerInfoUsername,
		BookerFullName:                   booking.OwnerInfoName,
		BookerEmail:                      booking.OwnerInfoEmail,
		BookerPhone:                      booking.OwnerInfoPhone,
		GuestName:                        booking.RoomsHolderFullName,
		GuestEmail:                       "", // Cần extract từ rooms.holder.email
		GuestPhone:                       booking.RoomsHolderPhone,
		BookingStatus:                    mapBookingStatus(booking.BookingStatus),
		Provider:                         "", // Sẽ được set từ hub orders
		AgentInvoiceInformation:          formatAgentInvoiceInfo(booking),
		CustomerInvoiceInformation:       booking.InvoicingInformation,
	}

	// Lookup hub order data and map new fields
	if hubOrder, exists := hubOrdersMap[booking.OrderCode]; exists {
		mapped.ExpediaReservationCode = hubOrder.ReservationCode
		mapped.Provider = mapProvider(hubOrder.Provider)

		// Map all new fields from hub_hotel_orders
		mapped.TotalNightlyRateAmount = extractFromJSON(hubOrder.TotalRateAmount, "occupancy_rate.total_nightly_rate.rate_amount")
		mapped.NightlyRateBasic = extractFromJSON(hubOrder.TotalRateBasic, "occupancy_rate.total_nightly_rate.rate_basic")
		mapped.NightlyTaxAmount = extractFromJSON(hubOrder.TotalTaxAmount, "occupancy_rate.total_nightly_rate.tax_amount")
		mapped.CurrencyAfterExchange = extractFromJSON(hubOrder.RateTaxCurrency, "occupancy_rate.total_nightly_rate.currency")
		mapped.HotelTaxesFees = hubOrder.RateTaxesTotal
		mapped.PayAtHotel = extractFromJSON(hubOrder.TotalPayAtHotel, "occupancy_rate.pay_at_hotel")
		mapped.Surcharges = extractFromJSON(hubOrder.DiscountOnBaseRateTotal, "occupancy_rate.surcharges")
		mapped.PriceBeforeDiscount = extractFromJSON(hubOrder.DiscountOnBaseRateTotal, "occupancy_rate.rate_discounts.amount")
		mapped.ProviderMarketingFee = extractFromJSON(hubOrder.TotalRateAmount, "occupancy_rate.marketing_fee")
		mapped.TotalRateAmountAllRoomsNights = hubOrder.TotalRateAmount
		mapped.TotalRateBasicAllRoomsNights = hubOrder.TotalRateBasic
		mapped.TotalTaxAmountAllRoomsNights = hubOrder.TotalTaxAmount
		mapped.SalePriceForHNHBiziHUB = hubOrder.PayNow
		mapped.TotalPayAtHotel = hubOrder.TotalPayAtHotel
		mapped.PayAtHotelCurrency = hubOrder.PayAtHotelCurrency
		mapped.TotalHiddenFeeHUBConfig = hubOrder.HiddenFeeAmount
		mapped.TotalDiscountFeeHUBConfig = hubOrder.DiscountAmount
		mapped.TotalWithTaxBeforeHiddenFee = hubOrder.AppliedHiddenFeeOriginalPrice
		mapped.TotalWithTaxAfterHiddenFee = hubOrder.AppliedHiddenFeeFinalPrice
		mapped.HiddenFeeID = extractFromJSON(hubOrder.AppliedHiddenFeeAmount, "hiddenfee.id")
		mapped.HiddenFeeType = extractFromJSON(hubOrder.AppliedHiddenFeeAmount, "hiddenfee.type")
		mapped.HiddenFeeLocation = hubOrder.AppliedHiddenFeeLocation
		mapped.HiddenFeeAccommodationType = hubOrder.AppliedHiddenFeeAccommodationType
		mapped.HiddenFeeRating = hubOrder.AppliedHiddenFeeRating
		mapped.HiddenFeeCountryCode = hubOrder.AppliedHiddenFeeCountryCode
		mapped.HiddenFeeHotelName = hubOrder.AppliedHiddenFeeHotelName
		mapped.HiddenFeeAmount = hubOrder.AppliedHiddenFeeAmount
		mapped.HiddenFeePercent = hubOrder.AppliedHiddenFeePercent
		mapped.HiddenFeeProvider = hubOrder.AppliedHiddenFeeProvider
		mapped.HiddenFeeOfficeID = extractFromJSON(hubOrder.AppliedHiddenFeeAmount, "hiddenfee.officeid")
		mapped.HiddenFeeCreatedAt = extractFromJSON(hubOrder.AppliedHiddenFeeAmount, "hiddenfee.createdat")
		mapped.HiddenFeeUpdatedAt = hubOrder.AppliedHiddenFeeUpdatedAt
		mapped.HiddenFeeHotelID = hubOrder.AppliedHiddenFeeHotelId
		mapped.DiscountID = hubOrder.AppliedDiscountId
		mapped.TotalWithTaxHiddenFeeBeforeDiscount = hubOrder.AppliedDiscountOriginalPrice
		mapped.TotalWithTaxHiddenFeeAfterDiscount = hubOrder.AppliedDiscountFinalPrice
		mapped.DiscountType = hubOrder.AppliedDiscountType
		mapped.DiscountLocation = hubOrder.AppliedDiscountLocation
		mapped.DiscountAccommodationType = hubOrder.AppliedDiscountAccommodationType
		mapped.DiscountRating = hubOrder.AppliedDiscountRating
		mapped.DiscountCountryCode = hubOrder.AppliedDiscountCountryCode
		mapped.DiscountHotelName = hubOrder.AppliedDiscountHotelName
		mapped.DiscountAmount = hubOrder.AppliedDiscountAmount
		mapped.DiscountPercent = hubOrder.AppliedDiscountPercent
		mapped.DiscountProvider = hubOrder.AppliedDiscountProvider
		mapped.DiscountOfficeID = hubOrder.AppliedDiscountOfficeId
		mapped.DiscountCreatedAt = hubOrder.AppliedDiscountCreatedAt
		mapped.DiscountUpdatedAt = hubOrder.AppliedDiscountUpdatedAt
		mapped.DiscountHotelID = extractFromJSON(hubOrder.AppliedDiscountAmount, "discount.hotelid")
		mapped.IsRefunded = hubOrder.Refunded
	}

	// Fields not available in database - leave empty
	mapped.ExchangeRate = ""
	mapped.Profit = ""

	return mapped
}

func extractGuestCount(occupancyInfo string) string {
	// Parse occupancy info like "1 người lớn & 0 trẻ em"
	if occupancyInfo == "" {
		return ""
	}

	parts := strings.Split(occupancyInfo, "&")
	if len(parts) < 2 {
		return occupancyInfo
	}

	// Extract adults
	adultsStr := strings.TrimSpace(parts[0])
	adults := extractNumber(adultsStr)

	// Extract children
	childrenStr := strings.TrimSpace(parts[1])
	children := extractNumber(childrenStr)

	totalGuests := adults + children
	return strconv.Itoa(totalGuests)
}

func extractNumber(text string) int {
	// Extract first number from text
	words := strings.Fields(text)
	for _, word := range words {
		if num, err := strconv.Atoi(word); err == nil {
			return num
		}
	}
	return 0
}

func mapBookingStatus(status string) string {
	if mapped, exists := bookingStatusMap[status]; exists {
		return mapped
	}
	return status // Return original if not found
}

func mapProvider(provider string) string {
	if mapped, exists := providerMap[provider]; exists {
		return mapped
	}
	return provider // Return original if not found
}

func extractFromJSON(jsonStr, path string) string {
	if jsonStr == "" {
		return ""
	}

	// Try to parse as JSON
	var data interface{}
	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		// If not valid JSON, return the original string
		return jsonStr
	}

	// Navigate through the path
	pathParts := strings.Split(path, ".")
	current := data

	for _, part := range pathParts {
		switch v := current.(type) {
		case map[string]interface{}:
			if val, exists := v[part]; exists {
				current = val
			} else {
				return ""
			}
		default:
			return ""
		}
	}

	// Convert result to string
	switch v := current.(type) {
	case string:
		return v
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64)
	case int:
		return strconv.Itoa(v)
	case bool:
		return strconv.FormatBool(v)
	default:
		return fmt.Sprintf("%v", v)
	}
}

func formatAgentInvoiceInfo(booking *HotelBooking) string {
	if booking.InvoiceAgentInfoCompanyName == "" {
		return ""
	}

	info := fmt.Sprintf("Company: %s", booking.InvoiceAgentInfoCompanyName)
	if booking.InvoiceAgentInfoCompanyTaxCode != "" {
		info += fmt.Sprintf(", Tax Code: %s", booking.InvoiceAgentInfoCompanyTaxCode)
	}
	if booking.InvoiceAgentInfoCompanyAddress != "" {
		info += fmt.Sprintf(", Address: %s", booking.InvoiceAgentInfoCompanyAddress)
	}
	if booking.InvoiceAgentInfoReceiverName != "" {
		info += fmt.Sprintf(", Receiver: %s", booking.InvoiceAgentInfoReceiverName)
	}
	if booking.InvoiceAgentInfoReceiverPhone != "" {
		info += fmt.Sprintf(", Phone: %s", booking.InvoiceAgentInfoReceiverPhone)
	}
	if booking.InvoiceAgentInfoReceiverEmail != "" {
		info += fmt.Sprintf(", Email: %s", booking.InvoiceAgentInfoReceiverEmail)
	}

	return info
}

func writeMappedRecords(records []*MappedRecord, filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// Write header
	header := []string{
		"Order Creation Time",
		"Payment Success Time",
		"Service Order Code",
		"HUB Order Code",
		"Hotel Reservation/Confirmation Code",
		"Expedia Reservation Code",
		"Agent/Customer Code",
		"Agent/Customer Name",
		"Branch Code",
		"Branch Name",
		"Check-in Time",
		"Check-out Time",
		"Hotel Name",
		"Destination",
		"Hotel Country Code",
		"Room Type",
		"Room Quantity",
		"Guest Count",
		"Number of Nights",
		"Username",
		"Booker Full Name",
		"Booker Email",
		"Booker Phone",
		"Guest Name (first room default)",
		"Guest Email",
		"Guest Phone",
		"Booking Status",
		"Provider",
		"Agent Invoice Information",
		"Customer Invoice Information",
		// New fields from hub_hotel_orders
		"Tổng giá phòng từng đêm bao gồm thuế phí (đã có hidden fee & discount)",
		"Giá phòng từng đêm chưa gồm thuế phí",
		"Thuế phòng từng đêm (đã có hidden fee & discount)",
		"Đơn vị tiền tệ sau chuyển đổi",
		"Thuế phí của KS",
		"Thanh toán tại nơi lưu trú",
		"Phụ phí",
		"Giá trước giảm",
		"Marketing_fee của NCC",
		"Tổng giá n phòng * n đêm bao gồm thuế phí (đã có hidden fee & discount)",
		"Tổng giá n phòng * n đêm chưa gồm thuế phí",
		"Tổng thuế phí của n phòng * n đêm (đã có hidden fee & discount)",
		"Giá bán cho HNH/BiziHUB",
		"Tổng thanh toán tại nơi lưu trú",
		"Đơn vị tiền tệ thanh toán tại nơi lưu trú",
		"Tổng hidden fee mà HUB config cho BZT/HNH",
		"Tổng discount fee mà HUB config cho BZT/HNH",
		"Tổng n phòng * n đêm đã gồm thuế phí (chưa cộng hidden_fee)",
		"Tổng n phòng * n đêm đã gồm thuế phí (đã cộng hidden_fee)",
		"ID hidden_fee",
		"Loại hidden_fee (1: theo điều kiện, 2: theo khách sạn)",
		"Location (1: quốc nội, 2: quốc tế)",
		"Loại chỗ ở hotel/resort",
		"Số sao",
		"Mã quốc gia",
		"Tên khách sạn",
		"Hidden_fee amount",
		"% hidden fee",
		"Mã NCC",
		"Mã Office ID",
		"Ngày tạo hidden fee",
		"Ngày cập nhật hidden fee",
		"Mã khách sạn",
		"ID discount",
		"Tổng n phòng * n đêm đã gồm thuế phí & hidden_fee (chưa cộng discount)",
		"Tổng n phòng * n đêm đã gồm thuế phí & hidden_fee (đã cộng discount)",
		"Loại discount (1: theo điều kiện, 2: theo khách sạn)",
		"Location discount (1: quốc nội, 2: quốc tế)",
		"Loại chỗ ở discount hotel/resort",
		"Số sao discount",
		"Mã quốc gia discount",
		"Tên khách sạn discount",
		"Discount amount",
		"% discount",
		"Mã NCC discount",
		"Mã Office ID discount",
		"Ngày tạo discount",
		"Ngày cập nhật discount",
		"Mã khách sạn discount",
		"Đã refund hay chưa",
		"Tỷ giá",
		"Lợi nhuận",
	}

	if err := writer.Write(header); err != nil {
		return err
	}

	// Write data rows
	for _, record := range records {
		row := []string{
			record.OrderCreationTime,
			record.PaymentSuccessTime,
			record.ServiceOrderCode,
			record.HUBOrderCode,
			record.HotelReservationConfirmationCode,
			record.ExpediaReservationCode,
			record.AgentCustomerCode,
			record.AgentCustomerName,
			record.BranchCode,
			record.BranchName,
			record.CheckInTime,
			record.CheckOutTime,
			record.HotelName,
			record.Destination,
			record.HotelCountryCode,
			record.RoomType,
			record.RoomQuantity,
			record.GuestCount,
			record.NumberOfNights,
			record.Username,
			record.BookerFullName,
			record.BookerEmail,
			record.BookerPhone,
			record.GuestName,
			record.GuestEmail,
			record.GuestPhone,
			record.BookingStatus,
			record.Provider,
			record.AgentInvoiceInformation,
			record.CustomerInvoiceInformation,
			// New fields from hub_hotel_orders
			record.TotalNightlyRateAmount,
			record.NightlyRateBasic,
			record.NightlyTaxAmount,
			record.CurrencyAfterExchange,
			record.HotelTaxesFees,
			record.PayAtHotel,
			record.Surcharges,
			record.PriceBeforeDiscount,
			record.ProviderMarketingFee,
			record.TotalRateAmountAllRoomsNights,
			record.TotalRateBasicAllRoomsNights,
			record.TotalTaxAmountAllRoomsNights,
			record.SalePriceForHNHBiziHUB,
			record.TotalPayAtHotel,
			record.PayAtHotelCurrency,
			record.TotalHiddenFeeHUBConfig,
			record.TotalDiscountFeeHUBConfig,
			record.TotalWithTaxBeforeHiddenFee,
			record.TotalWithTaxAfterHiddenFee,
			record.HiddenFeeID,
			record.HiddenFeeType,
			record.HiddenFeeLocation,
			record.HiddenFeeAccommodationType,
			record.HiddenFeeRating,
			record.HiddenFeeCountryCode,
			record.HiddenFeeHotelName,
			record.HiddenFeeAmount,
			record.HiddenFeePercent,
			record.HiddenFeeProvider,
			record.HiddenFeeOfficeID,
			record.HiddenFeeCreatedAt,
			record.HiddenFeeUpdatedAt,
			record.HiddenFeeHotelID,
			record.DiscountID,
			record.TotalWithTaxHiddenFeeBeforeDiscount,
			record.TotalWithTaxHiddenFeeAfterDiscount,
			record.DiscountType,
			record.DiscountLocation,
			record.DiscountAccommodationType,
			record.DiscountRating,
			record.DiscountCountryCode,
			record.DiscountHotelName,
			record.DiscountAmount,
			record.DiscountPercent,
			record.DiscountProvider,
			record.DiscountOfficeID,
			record.DiscountCreatedAt,
			record.DiscountUpdatedAt,
			record.DiscountHotelID,
			record.IsRefunded,
			record.ExchangeRate,
			record.Profit,
		}

		if err := writer.Write(row); err != nil {
			return err
		}
	}

	return nil
}
