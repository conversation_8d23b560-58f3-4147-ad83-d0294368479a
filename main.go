package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"os"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Result structure to track update results
type Result struct {
	Success int64
	Error   int64
	NoMatch int64
}

func main() {
	startTime := time.Now()

	// Cấu hình
	mongoURI := "*****************************************************************************************************************" // Cập nhật connection string của bạn
	dbName := "common-db-v2"
	collName := "hotels-v2"
	batchSize := 100                   // Số lượng hotel ID trong mỗi batch
	numWorkers := runtime.NumCPU() / 2 // Sử dụng số lượng CPU cores

	// Đọc file JSON
	jsonFile, err := os.Open("list_delete_dida_id.json")
	if err != nil {
		log.Fatalf("Error opening JSON file: %v", err)
	}
	defer jsonFile.Close()

	byteValue, err := io.ReadAll(jsonFile)
	if err != nil {
		log.Fatalf("Error reading JSON file: %v", err)
	}

	var hotelIDs []string
	err = json.Unmarshal(byteValue, &hotelIDs)
	if err != nil {
		log.Fatalf("Error parsing JSON: %v", err)
	}

	totalHotels := len(hotelIDs)
	fmt.Printf("Found %d hotel IDs to process\n", totalHotels)

	// Kết nối MongoDB với cấu hình tối ưu
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Hour)
	defer cancel()

	clientOptions := options.Client().
		ApplyURI(mongoURI).
		SetMaxPoolSize(uint64(numWorkers * 2)). // Tăng kích thước connection pool
		SetMaxConnIdleTime(30 * time.Minute).
		SetRetryWrites(true)

	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		log.Fatalf("Error connecting to MongoDB: %v", err)
	}
	defer client.Disconnect(ctx)

	err = client.Ping(ctx, nil)
	if err != nil {
		log.Fatalf("Error connecting to MongoDB: %v", err)
	}
	fmt.Println("Connected to MongoDB!")

	collection := client.Database(dbName).Collection(collName)

	// Tạo các batches từ danh sách hotel IDs
	batches := make([][]string, 0)
	for i := 0; i < len(hotelIDs); i += batchSize {
		end := i + batchSize
		if end > len(hotelIDs) {
			end = len(hotelIDs)
		}
		batches = append(batches, hotelIDs[i:end])
	}
	fmt.Printf("Created %d batches of approximately %d hotels each\n", len(batches), batchSize)

	// Thiết lập các biến đồng bộ
	var wg sync.WaitGroup
	results := &Result{}

	// Kênh để theo dõi tiến trình
	progressChan := make(chan int, numWorkers*10)
	doneChan := make(chan bool)

	// Kênh để phân phối công việc
	batchChan := make(chan []string, len(batches))

	// Bắt đầu theo dõi tiến trình
	go func() {
		processed := 0
		progressInterval := 100
		startTime := time.Now()

		for count := range progressChan {
			processed += count

			if processed%progressInterval == 0 || processed == totalHotels {
				elapsed := time.Since(startTime)
				rate := float64(processed) / elapsed.Seconds()
				percentComplete := float64(processed) / float64(totalHotels) * 100

				fmt.Printf("Progress: %d/%d hotels processed (%.2f%%) - %.2f hotels/sec\n",
					processed, totalHotels, percentComplete, rate)
			}

			if processed >= totalHotels {
				doneChan <- true
				return
			}
		}
	}()

	// Bắt đầu worker pool
	fmt.Printf("Starting %d workers to process hotels with bulk operations...\n", numWorkers)
	wg.Add(numWorkers)

	// Tạo các worker
	for i := 0; i < numWorkers; i++ {
		go func() {
			defer wg.Done()

			// Ngôn ngữ cần xử lý cho mỗi hotel
			languages := []string{"vi-VN", "en-US"}

			// Xử lý các batch từ kênh
			for batch := range batchChan {
				// Tạo bulk operation
				bulkOps := make([]mongo.WriteModel, 0, len(batch)*2) // *2 cho hai ngôn ngữ

				// Thêm các thao tác vào bulk write
				for _, hotelID := range batch {
					// Xử lý cả hai phiên bản ngôn ngữ
					for _, lang := range languages {
						filter := bson.M{
							"hotel_id": hotelID,
							"language": lang,
						}

						update := bson.M{
							"$set": bson.M{
								"provider_ids.16": "",
							},
						}

						// Tạo update model
						updateModel := mongo.NewUpdateOneModel().
							SetFilter(filter).
							SetUpdate(update).
							SetUpsert(false)

						bulkOps = append(bulkOps, updateModel)
					}
				}

				// Thực hiện bulk write nếu có thao tác
				if len(bulkOps) > 0 {
					bulkResult, err := collection.BulkWrite(ctx, bulkOps)
					if err != nil {
						log.Printf("Error in bulk write: %v", err)
						atomic.AddInt64(&results.Error, int64(len(bulkOps)))
						continue
					}

					// Cập nhật thống kê
					atomic.AddInt64(&results.Success, bulkResult.ModifiedCount)
					atomic.AddInt64(&results.NoMatch, bulkResult.MatchedCount-bulkResult.ModifiedCount)
				}

				// Báo cáo tiến trình (số lượng hotel đã xử lý trong batch này)
				progressChan <- len(batch)
			}
		}()
	}

	// Đưa tất cả các batch vào kênh
	for _, batch := range batches {
		batchChan <- batch
	}
	close(batchChan) // Báo hiệu không còn batch nào được thêm vào

	// Đợi tất cả worker hoàn thành
	wg.Wait()
	close(progressChan)
	<-doneChan // Đợi reporter hoàn thành

	// Tính toán tổng thời gian
	elapsed := time.Since(startTime)

	// In tóm tắt
	fmt.Printf("\nUpdate completed in %s\n", elapsed)
	fmt.Printf("Successfully updated: %d documents\n", results.Success)
	fmt.Printf("No matches found: %d documents\n", results.NoMatch)
	fmt.Printf("Errors encountered: %d documents\n", results.Error)

	// Tính toán tốc độ
	docsPerSecond := float64(totalHotels*2) / elapsed.Seconds() // *2 vì chúng ta xử lý 2 ngôn ngữ cho mỗi hotel
	fmt.Printf("Processing rate: %.2f documents/second\n", docsPerSecond)
}
